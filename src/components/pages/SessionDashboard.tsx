/**
 * Session Dashboard Component
 * 
 * Main dashboard that integrates all session-based functionality including
 * session management, timer instances, inactivity detection, and notes.
 */

import { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Alert,
  Fab,
  Backdrop,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
} from '@mui/icons-material';
import { SessionTimerBar } from '../layout/SessionTimerBar';
import { SessionList } from '../ui/sessions/SessionList';
import { SessionTimerDisplay } from '../ui/display/SessionTimerDisplay';
import { InactivityWarningDialog } from '../ui/dialogs/InactivityWarningDialog';
import { EnhancedNotesDialog } from '../ui/dialogs/EnhancedNotesDialog';
import { useSessionManagement } from '../../hooks/useSessionManagement';
import { useInactivityDetection } from '../../hooks/useInactivityDetection';
import { useTasks } from '../../hooks/useTasks';
import { useNotification } from '../../contexts/NotificationContext';
import { TaskSession, TimerInstance } from '../../types/timer';
import { Task } from '../../types/task';

export function SessionDashboard() {
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [notesDialogOpen, setNotesDialogOpen] = useState(false);
  const [notesContext, setNotesContext] = useState<{
    task: Task | null;
    session: TaskSession | null;
    timerInstance: TimerInstance | null;
    level: 'task' | 'session' | 'timer';
  }>({
    task: null,
    session: null,
    timerInstance: null,
    level: 'task',
  });

  // Hooks
  const {
    sessions,
    activeSession,
    createSession,
    updateSession,
    deleteSession,
    setActiveSession,
    createTimerInstance,
    startTimerInstance,
    stopTimerInstance,
    isLoading: sessionsLoading,
    error: sessionsError,
  } = useSessionManagement();

  const {
    isWarningShown,
    warningTimeRemaining,

  } = useInactivityDetection();

  const { tasks, isLoading: tasksLoading } = useTasks();
  const { showError, showSuccess } = useNotification();

  // Handle inactivity warning
  const handleContinueWork = () => {
    window.dispatchEvent(new CustomEvent('timer-continue-activity', {
      detail: { reason: 'user-action', timestamp: new Date() }
    }));
  };

  const handlePauseFromInactivity = () => {
    window.dispatchEvent(new CustomEvent('timer-pause-inactivity', {
      detail: { reason: 'user-action', timestamp: new Date() }
    }));
  };

  // Session management handlers
  const handleStartSession = async (taskId: string, taskName: string): Promise<TaskSession> => {
    try {
      const newSession = await createSession(taskId, taskName);
      await setActiveSession(newSession);
      return newSession;
    } catch (error) {
      showError('Failed to start session');
      throw error;
    }
  };

  const handleStopSession = async (): Promise<void> => {
    try {
      await setActiveSession(null);
      showSuccess('Session stopped');
    } catch (error) {
      showError('Failed to stop session');
      throw error;
    }
  };

  const handleCreateTimerInstance = async (sessionId: string): Promise<TimerInstance> => {
    try {
      return await createTimerInstance(sessionId);
    } catch (error) {
      showError('Failed to create timer instance');
      throw error;
    }
  };

  const handleStartTimer = async (instanceId: string): Promise<void> => {
    try {
      await startTimerInstance(instanceId);
    } catch (error) {
      showError('Failed to start timer');
      throw error;
    }
  };

  const handleStopTimer = async (instanceId: string): Promise<void> => {
    try {
      await stopTimerInstance(instanceId);
    } catch (error) {
      showError('Failed to stop timer');
      throw error;
    }
  };

  const handlePauseTimer = async (): Promise<void> => {
    // TODO: Implement pause functionality
    showError('Pause functionality not yet implemented');
  };

  const handleResumeTimer = async (): Promise<void> => {
    // TODO: Implement resume functionality
    showError('Resume functionality not yet implemented');
  };

  // Notes handlers
  const handleOpenSessionNotes = (sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId);
    const task = tasks.find(t => t.id === session?.taskId);
    
    if (session && task) {
      setNotesContext({
        task,
        session,
        timerInstance: null,
        level: 'session',
      });
      setNotesDialogOpen(true);
    }
  };

  const handleOpenTimerNotes = (sessionId: string, instanceId?: string) => {
    const session = sessions.find(s => s.id === sessionId);
    const task = tasks.find(t => t.id === session?.taskId);
    const timerInstance = instanceId 
      ? session?.timerInstances.find(i => i.id === instanceId) || null
      : null;
    
    if (session && task) {
      setNotesContext({
        task,
        session,
        timerInstance,
        level: instanceId ? 'timer' : 'session',
      });
      setNotesDialogOpen(true);
    }
  };

  const handleSelectSession = (session: TaskSession) => {
    setSelectedSessionId(session.id);
  };

  const handleStartSelectedSession = async (session: TaskSession) => {
    try {
      await setActiveSession(session);
      showSuccess(`Activated session for "${session.taskName}"`);
    } catch (error) {
      showError('Failed to activate session');
    }
  };

  // Show loading state
  if (sessionsLoading || tasksLoading) {
    return (
      <Backdrop open sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <CircularProgress color="inherit" />
      </Backdrop>
    );
  }

  // Show error state
  if (sessionsError) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {sessionsError}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Session Timer Bar */}
      <Box sx={{ mb: 4 }}>
        <SessionTimerBar
          activeSession={activeSession}
          predefinedTasks={tasks}
          onStartSession={handleStartSession}
          onStopSession={handleStopSession}
          onCreateTimerInstance={handleCreateTimerInstance}
          onStartTimer={handleStartTimer}
          onStopTimer={handleStopTimer}
          onPauseTimer={handlePauseTimer}
          onResumeTimer={handleResumeTimer}
          onOpenSessionNotes={handleOpenSessionNotes}
          onOpenTimerNotes={(instanceId) => handleOpenTimerNotes(activeSession?.id || '', instanceId)}
        />
      </Box>

      <Box sx={{ display: 'flex', gap: 4, flexDirection: { xs: 'column', md: 'row' } }}>
        {/* Active Session Display */}
        {activeSession && (
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Active Session
            </Typography>
            <SessionTimerDisplay
              session={activeSession}
              showControls
              showNotes
              size="large"
              onStartTimer={handleStartTimer}
              onStopTimer={handleStopTimer}
              onPauseTimer={handlePauseTimer}
              onResumeTimer={handleResumeTimer}
              onOpenNotes={handleOpenTimerNotes}
            />
          </Box>
        )}

        {/* Session List */}
        <Box sx={{ flex: activeSession ? 1 : 2 }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            All Sessions
          </Typography>
          <SessionList
            sessions={sessions}
            onSelectSession={handleSelectSession}
            onDeleteSession={deleteSession}
            onUpdateSession={updateSession}
            onStartSession={handleStartSelectedSession}
            onStopSession={handleStopSession}
            selectedSessionId={selectedSessionId || undefined}
            isLoading={sessionsLoading}
          />
        </Box>
      </Box>

      {/* Inactivity Warning Dialog */}
      {isWarningShown && activeSession && (
        <InactivityWarningDialog
          remainingSeconds={warningTimeRemaining}
          onContinue={handleContinueWork}
          onPause={handlePauseFromInactivity}
          timerInstance={{} as TimerInstance} // Placeholder - should be actual running instance
        />
      )}

      {/* Enhanced Notes Dialog */}
      <EnhancedNotesDialog
        open={notesDialogOpen}
        onClose={() => setNotesDialogOpen(false)}
        task={notesContext.task!}
        session={notesContext.session}
        timerInstance={notesContext.timerInstance}
        initialNoteLevel={notesContext.level}
      />

      {/* Floating Action Button for Quick Session Creation */}
      <Fab
        color="primary"
        aria-label="create session"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
        }}
        onClick={() => {
          // TODO: Implement quick session creation dialog
          showError('Quick session creation not yet implemented');
        }}
      >
        <AddIcon />
      </Fab>
    </Container>
  );
}
