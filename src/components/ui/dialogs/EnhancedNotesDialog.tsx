/**
 * Enhanced Notes Dialog
 * 
 * Supports creating and managing notes at different levels:
 * - Task-level notes (general task documentation)
 * - Session-level notes (notes for a specific task session)
 * - Timer-level notes (notes for individual timer instances)
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  Tabs,
  Tab,
  Chip,
  Stack,
  Divider,
  Alert,
  IconButton,
} from '@mui/material';
import {
  Close as CloseIcon,
  Notes as NotesIcon,
  Timer as TimerIcon,
  FolderOpen as SessionIcon,
  Assignment as TaskIcon,
} from '@mui/icons-material';
import { TaskNote, NoteTemplate } from '../../../types/notes';
import { TaskSession, TimerInstance } from '../../../types/timer';
import { Task } from '../../../types/task';
import { NoteEditor } from '../../features/notes/NoteEditor';
import { useTaskNotes } from '../../../hooks/useTaskNotes';
import { useNoteTemplates } from '../../../hooks/useNoteTemplates';

interface EnhancedNotesDialogProps {
  open: boolean;
  onClose: () => void;
  task: Task | null;
  session?: TaskSession | null;
  timerInstance?: TimerInstance | null;
  initialNoteLevel?: 'task' | 'session' | 'timer';
}

type NoteLevel = 'task' | 'session' | 'timer';

export function EnhancedNotesDialog({
  open,
  onClose,
  task,
  session,
  timerInstance,
  initialNoteLevel = 'task',
}: EnhancedNotesDialogProps) {
  const [activeTab, setActiveTab] = useState<NoteLevel>(initialNoteLevel);
  const [selectedTemplate, setSelectedTemplate] = useState<NoteTemplate | null>(null);
  const [isEditorCollapsed, setIsEditorCollapsed] = useState(false);

  const { createNote, updateNote, deleteNote, getNotesByTaskId, getNotesBySessionId, getNotesByTimerInstanceId } = useTaskNotes();
  const { getActiveTemplates } = useNoteTemplates();

  // Get notes for each level
  const taskNotes = task ? getNotesByTaskId(task.id).filter(n => n.noteLevel === 'task') : [];
  const sessionNotes = session ? getNotesBySessionId(session.id).filter(n => n.noteLevel === 'session') : [];
  const timerNotes = timerInstance ? getNotesByTimerInstanceId(timerInstance.id).filter(n => n.noteLevel === 'timer') : [];

  // Available tabs based on context
  const availableTabs = [
    ...(task ? [{ value: 'task' as const, label: 'Task Notes', icon: <TaskIcon />, count: taskNotes.length }] : []),
    ...(session ? [{ value: 'session' as const, label: 'Session Notes', icon: <SessionIcon />, count: sessionNotes.length }] : []),
    ...(timerInstance ? [{ value: 'timer' as const, label: 'Timer Notes', icon: <TimerIcon />, count: timerNotes.length }] : []),
  ];

  // Reset tab if context changes
  useEffect(() => {
    if (!availableTabs.find(tab => tab.value === activeTab)) {
      // Set to the first available tab, or 'task' as fallback
      const firstAvailableTab = availableTabs[0]?.value || 'task';
      setActiveTab(firstAvailableTab);
    }
  }, [session, timerInstance, activeTab, availableTabs]);

  const handleTabChange = (_: React.SyntheticEvent, newValue: NoteLevel) => {
    setActiveTab(newValue);
    setSelectedTemplate(null);
  };

  const handleCreateNote = async (noteData: Omit<TaskNote, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!task) return;

    const enhancedNoteData = {
      ...noteData,
      taskId: task.id,
      noteLevel: activeTab,
      sessionId: activeTab === 'session' ? session?.id || null : null,
      timerInstanceId: activeTab === 'timer' ? timerInstance?.id || null : null,
    };

    return await createNote(enhancedNoteData);
  };

  const getCurrentNotes = () => {
    switch (activeTab) {
      case 'task':
        return taskNotes;
      case 'session':
        return sessionNotes;
      case 'timer':
        return timerNotes;
      default:
        return [];
    }
  };

  const getContextInfo = () => {
    switch (activeTab) {
      case 'task':
        return {
          title: `Task Notes - ${task?.name || 'Unknown Task'}`,
          description: 'General notes and documentation for this task',
        };
      case 'session':
        return {
          title: `Session Notes - ${session?.taskName || 'Unknown Session'}`,
          description: `Notes for session on ${session?.date || 'Unknown Date'}`,
        };
      case 'timer':
        return {
          title: `Timer Notes - Instance ${timerInstance?.id?.slice(-8) || 'Unknown'}`,
          description: 'Notes for this specific timer instance',
        };
      default:
        return { title: 'Notes', description: '' };
    }
  };

  const contextInfo = getContextInfo();
  const currentNotes = getCurrentNotes();
  const activeTemplates = getActiveTemplates();

  // Don't render if no context is available
  if (availableTabs.length === 0) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '80vh', display: 'flex', flexDirection: 'column' }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        pb: 1 
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <NotesIcon />
          <Typography variant="h6">{contextInfo.title}</Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', p: 0 }}>
        {/* Tabs for different note levels */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            {availableTabs.map((tab) => (
              <Tab
                key={tab.value}
                value={tab.value}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {tab.icon}
                    {tab.label}
                    {tab.count > 0 && (
                      <Chip size="small" label={tab.count} color="primary" />
                    )}
                  </Box>
                }
              />
            ))}
          </Tabs>
        </Box>

        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', p: 3 }}>
          {/* Context description */}
          <Alert severity="info" sx={{ mb: 2 }}>
            {contextInfo.description}
          </Alert>

          {/* Note editor */}
          {activeTemplates.length > 0 ? (
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Create New Note
              </Typography>
              
              {/* Template selection */}
              <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                <Button
                  variant={selectedTemplate === null ? 'contained' : 'outlined'}
                  onClick={() => setSelectedTemplate(null)}
                  size="small"
                >
                  No Template
                </Button>
                {activeTemplates.map((template) => (
                  <Button
                    key={template.id}
                    variant={selectedTemplate?.id === template.id ? 'contained' : 'outlined'}
                    onClick={() => setSelectedTemplate(template)}
                    size="small"
                  >
                    {template.name}
                  </Button>
                ))}
              </Stack>

              {task && (
                <NoteEditor
                  taskId={task.id}
                  template={selectedTemplate}
                  sessionId={activeTab === 'session' ? session?.id || null : null}
                  timerInstanceId={activeTab === 'timer' ? timerInstance?.id || null : null}
                  noteLevel={activeTab}
                  onSaveNote={handleCreateNote}
                  onUpdateNote={updateNote}
                  onDeleteNote={deleteNote}
                />
              )}
                isCollapsed={isEditorCollapsed}
                onToggleCollapse={() => setIsEditorCollapsed(!isEditorCollapsed)}
                onCloseEditor={() => setSelectedTemplate(null)}
              />
            </Box>
          ) : (
            <Alert severity="warning" sx={{ mb: 2 }}>
              No note templates available. Create templates in Settings to start adding notes.
            </Alert>
          )}

          <Divider sx={{ my: 2 }} />

          {/* Existing notes */}
          <Typography variant="h6" sx={{ mb: 2 }}>
            Existing Notes ({currentNotes.length})
          </Typography>

          {currentNotes.length === 0 ? (
            <Alert severity="info">
              No notes found for this {activeTab}. Create your first note above!
            </Alert>
          ) : (
            <Stack spacing={2}>
              {currentNotes.map((note) => (
                <NoteCard
                  key={note.id}
                  note={note}
                  onEdit={(editedNote) => {
                    // TODO: Implement note editing
                    console.log('Edit note:', editedNote);
                  }}
                  onDelete={() => deleteNote(note.id)}
                />
              ))}
            </Stack>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

// Note Card Component
interface NoteCardProps {
  note: TaskNote;
  onEdit: (note: TaskNote) => void;
  onDelete: () => void;
}

function NoteCard({ note }: NoteCardProps) {
  return (
    <Box
      sx={{
        p: 2,
        border: 1,
        borderColor: 'divider',
        borderRadius: 1,
        bgcolor: 'background.paper',
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
          {note.templateName}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {new Date(note.createdAt).toLocaleDateString()}
        </Typography>
      </Box>
      
      {/* Display field values */}
      <Stack spacing={1}>
        {Object.entries(note.fieldValues).map(([fieldId, value]) => (
          <Box key={fieldId}>
            <Typography variant="body2" color="text.secondary">
              {fieldId}: {String(value)}
            </Typography>
          </Box>
        ))}
      </Stack>
    </Box>
  );
}
